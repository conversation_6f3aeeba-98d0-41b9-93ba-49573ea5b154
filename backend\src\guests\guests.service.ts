import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Guest } from './guest.entity';
import { generateUUID7 } from '../utils/uuid';

@Injectable()
export class GuestsService {
  constructor(
    @InjectRepository(Guest)
    private guestsRepository: Repository<Guest>,
  ) {}

  async findAll(): Promise<Guest[]> {
    return this.guestsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(uuid: string): Promise<Guest> {
    const guest = await this.guestsRepository.findOne({
      where: { uuid, isDeleted: false },
    });
    
    if (!guest) {
      throw new NotFoundException(`Guest with UUID ${uuid} not found`);
    }
    
    return guest;
  }

  async findByStoreId(storeId: string): Promise<Guest[]> {
    return this.guestsRepository.find({
      where: { storeId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async create(createGuestDto: Partial<Guest> & { userId?: string | number }): Promise<Guest> {
    // Extract userId and map it to the correct fields
    const { userId, ...guestData } = createGuestDto;

    // Create the guest with proper field mapping and auto-generated UUID7
    const guest = this.guestsRepository.create({
      ...guestData,
      uuid: generateUUID7(), // Auto-generate UUID7 for the guest
      createdBy: userId?.toString(),
    });

    return this.guestsRepository.save(guest);
  }

  async update(uuid: string, updateGuestDto: Partial<Guest>): Promise<Guest> {
    const guest = await this.findOne(uuid);
    Object.assign(guest, updateGuestDto);
    return this.guestsRepository.save(guest);
  }

  async remove(uuid: string): Promise<void> {
    const guest = await this.findOne(uuid);
    guest.isDeleted = true;
    await this.guestsRepository.save(guest);
  }

  async findOrCreate(guestData: Partial<Guest> & { storeId: string }): Promise<Guest> {
    // Try to find existing guest by email or phone if provided
    let existingGuest: Guest | null = null;
    
    if (guestData.email) {
      existingGuest = await this.guestsRepository.findOne({
        where: { 
          email: guestData.email, 
          storeId: guestData.storeId,
          isDeleted: false 
        },
      });
    }
    
    if (!existingGuest && guestData.phone) {
      existingGuest = await this.guestsRepository.findOne({
        where: { 
          phone: guestData.phone, 
          storeId: guestData.storeId,
          isDeleted: false 
        },
      });
    }

    if (existingGuest) {
      // Update existing guest with new data
      Object.assign(existingGuest, guestData);
      return this.guestsRepository.save(existingGuest);
    }

    // Create new guest
    return this.create(guestData);
  }
}
