import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne } from 'typeorm';
import { StoreStatus } from '../shared/enums';

@Entity('stores')
export class Store {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  currency: string;

  @Column({ type: 'varchar', nullable: true })
  preferredLanguage: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', nullable: true })
  logo: string;

  @Column({ type: 'varchar', nullable: true })
  website: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  state: string;

  @Column({ type: 'varchar', nullable: true })
  zipCode: string;

  @Column({ type: 'varchar', nullable: true })
  country: string;

  @Column({ type: 'enum', enum: StoreStatus, default: StoreStatus.ACTIVE })
  status: StoreStatus;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  ownerId: string;

  @Column({ type: 'bigint', nullable: true })
  managerId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.ownedStores)
  owner: any;

  @ManyToOne('User', (user: any) => user.managedStores)
  manager: any;

  @ManyToOne('User', (user: any) => user.createdStores)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedStores)
  updatedByUser: any;

  @OneToMany('Product', (product: any) => product.store)
  products: any[];

  @OneToMany('Customer', (customer: any) => customer.store)
  customers: any[];

  @OneToMany('Guest', (guest: any) => guest.store)
  guests: any[];

  @OneToMany('Order', (order: any) => order.store)
  orders: any[];

  @OneToMany('Conversation', (conversation: any) => conversation.store)
  conversations: any[];

  @OneToMany('Agent', (agent: any) => agent.store)
  agents: any[];
}
