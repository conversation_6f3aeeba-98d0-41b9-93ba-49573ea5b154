{"version": 3, "file": "conversation.entity.js", "sourceRoot": "", "sources": ["../../src/conversations/conversation.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAA2H;AAGpH,IAAM,YAAY,GAAlB,MAAM,YAAY;CAsExB,CAAA;AAtEY,oCAAY;AAEvB;IADC,IAAA,gCAAsB,EAAC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;;wCAC7C;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;0CAC7B;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;;2CACd;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACrB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC7B;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;+CACzB;AAGnB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;+CAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;+CAAC;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;;+CACT;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;;+CACzB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC5B;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACxB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAC1B;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;;6CACX;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC3B;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAC5B;AAIb;IADC,IAAA,mBAAS,EAAC,MAAM,EAAE,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC;;0CAC3C;AAGV;IADC,IAAA,mBAAS,EAAC,UAAU,EAAE,CAAC,QAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC;;8CACnD;AAGd;IADC,IAAA,mBAAS,EAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC;;2CAC7C;AAGX;IADC,IAAA,mBAAS,EAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC;;2CAC7C;AAGX;IADC,IAAA,mBAAS,EAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC;;2CAC7C;AAGX;IADC,IAAA,mBAAS,EAAC,SAAS,EAAE,CAAC,OAAY,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC;;8CAC7C;AAGhB;IADC,IAAA,mBAAS,EAAC,UAAU,EAAE,CAAC,QAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;;+CAC/C;uBArEN,YAAY;IADxB,IAAA,gBAAM,EAAC,eAAe,CAAC;GACX,YAAY,CAsExB"}