"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Guest = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("../utils/uuid");
let Guest = class Guest {
    constructor() {
        if (!this.uuid) {
            this.uuid = (0, uuid_1.generateUUID7)();
        }
    }
};
exports.Guest = Guest;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar' }),
    __metadata("design:type", String)
], Guest.prototype, "uuid", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "state", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "zipCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "sessionData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Guest.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Guest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Guest.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', nullable: true }),
    __metadata("design:type", String)
], Guest.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'bigint' }),
    __metadata("design:type", String)
], Guest.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", String)
], Guest.prototype, "storeId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('User', (user) => user.createdGuests),
    __metadata("design:type", Object)
], Guest.prototype, "createdByUser", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('User', (user) => user.updatedGuests),
    __metadata("design:type", Object)
], Guest.prototype, "updatedByUser", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('Store', (store) => store.guests),
    __metadata("design:type", Object)
], Guest.prototype, "store", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Order', (order) => order.guest),
    __metadata("design:type", Array)
], Guest.prototype, "orders", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Conversation', (conversation) => conversation.guest),
    __metadata("design:type", Array)
], Guest.prototype, "conversations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('Message', (message) => message.guest),
    __metadata("design:type", Array)
], Guest.prototype, "messages", void 0);
__decorate([
    (0, typeorm_1.OneToMany)('ToolCall', (toolCall) => toolCall.guest),
    __metadata("design:type", Array)
], Guest.prototype, "toolCalls", void 0);
exports.Guest = Guest = __decorate([
    (0, typeorm_1.Entity)('guests'),
    __metadata("design:paramtypes", [])
], Guest);
//# sourceMappingURL=guest.entity.js.map