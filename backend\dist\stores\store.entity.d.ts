import { StoreStatus } from '../shared/enums';
export declare class Store {
    id: string;
    name: string;
    currency: string;
    preferredLanguage: string;
    description: string;
    logo: string;
    website: string;
    phone: string;
    email: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    status: StoreStatus;
    isDeleted: boolean;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
    updatedBy: string;
    ownerId: string;
    managerId: string;
    owner: any;
    manager: any;
    createdByUser: any;
    updatedByUser: any;
    products: any[];
    customers: any[];
    guests: any[];
    orders: any[];
    conversations: any[];
    agents: any[];
}
