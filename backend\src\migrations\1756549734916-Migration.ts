import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1756549734916 implements MigrationInterface {
    name = 'Migration1756549734916'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "guests" ("uuid" character varying NOT NULL, "name" character varying, "email" character varying, "phone" character varying, "address" text, "city" character varying, "state" character varying, "zipCode" character varying, "country" character varying, "notes" text, "sessionData" character varying, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" bigint, "updatedBy" bigint, "storeId" bigint NOT NULL, "createdByUserId" bigint, "updatedByUserId" bigint, CONSTRAINT "PK_9088165c47665ec04d83579089a" PRIMARY KEY ("uuid"))`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD "customerId" bigint`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "customerId" bigint`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD "customerId" bigint`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_e5de51ca888d8b1f5ac25799dd1"`);
        await queryRunner.query(`ALTER TYPE "public"."orders_status_enum" RENAME TO "orders_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('draft', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" TYPE "public"."orders_status_enum" USING "status"::"text"::"public"."orders_status_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."orders_priority_enum" RENAME TO "orders_priority_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."orders_priority_enum" AS ENUM('low', 'normal', 'high', 'urgent')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" TYPE "public"."orders_priority_enum" USING "priority"::"text"::"public"."orders_priority_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" SET DEFAULT 'normal'`);
        await queryRunner.query(`DROP TYPE "public"."orders_priority_enum_old"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "customerId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP CONSTRAINT "FK_867978811f704eb20e1946cb47b"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_4838cd4fc48a6ff2d4aa01aa646"`);
        await queryRunner.query(`ALTER TABLE "messages" ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP CONSTRAINT "FK_a9b3b5d51da1c75242055338b59"`);
        await queryRunner.query(`ALTER TABLE "conversations" ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_e5de51ca888d8b1f5ac25799dd1" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_2acd0cb548935db1f87699fb603" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD CONSTRAINT "FK_867978811f704eb20e1946cb47b" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD CONSTRAINT "FK_b34008eb13164c0757125ecaab2" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD CONSTRAINT "FK_da55afc7c13e92e89400da4f3de" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "guests" ADD CONSTRAINT "FK_012414fe9d94f52b9bbf7858dc5" FOREIGN KEY ("createdByUserId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "guests" ADD CONSTRAINT "FK_ffbb2187d95e142d3871e69dee1" FOREIGN KEY ("updatedByUserId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "guests" ADD CONSTRAINT "FK_4bff229fd41f62ee3bd6a963e78" FOREIGN KEY ("storeId") REFERENCES "stores"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_4838cd4fc48a6ff2d4aa01aa646" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_cd3cd1906c2198f36dc5e7fe4d4" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_bbe8cd572034dc5002cb6933682" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD CONSTRAINT "FK_a9b3b5d51da1c75242055338b59" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD CONSTRAINT "FK_5a4866f304edf4591ad785d34a4" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD CONSTRAINT "FK_1bb8b7dda222aa941c40bc681d2" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "guests"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP COLUMN "customerId"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "customerId"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "customerId"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_e5de51ca888d8b1f5ac25799dd1" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TYPE "public"."orders_status_enum_old" RENAME TO "orders_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" TYPE "public"."orders_status_enum_old" USING "status"::"text"::"public"."orders_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum_old" AS ENUM('draft', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned')`);
        await queryRunner.query(`ALTER TYPE "public"."orders_priority_enum_old" RENAME TO "orders_priority_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_priority_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" SET DEFAULT 'normal'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" TYPE "public"."orders_priority_enum_old" USING "priority"::"text"::"public"."orders_priority_enum_old"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."orders_priority_enum_old" AS ENUM('low', 'normal', 'high', 'urgent')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "customerId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD CONSTRAINT "FK_867978811f704eb20e1946cb47b" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_4838cd4fc48a6ff2d4aa01aa646" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD CONSTRAINT "FK_a9b3b5d51da1c75242055338b59" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations" ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_e5de51ca888d8b1f5ac25799dd1"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_2acd0cb548935db1f87699fb603"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP CONSTRAINT "FK_867978811f704eb20e1946cb47b"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP CONSTRAINT "FK_b34008eb13164c0757125ecaab2"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP CONSTRAINT "FK_da55afc7c13e92e89400da4f3de"`);
        await queryRunner.query(`ALTER TABLE "guests" DROP CONSTRAINT "FK_012414fe9d94f52b9bbf7858dc5"`);
        await queryRunner.query(`ALTER TABLE "guests" DROP CONSTRAINT "FK_ffbb2187d95e142d3871e69dee1"`);
        await queryRunner.query(`ALTER TABLE "guests" DROP CONSTRAINT "FK_4bff229fd41f62ee3bd6a963e78"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_4838cd4fc48a6ff2d4aa01aa646"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_cd3cd1906c2198f36dc5e7fe4d4"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_bbe8cd572034dc5002cb6933682"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP CONSTRAINT "FK_a9b3b5d51da1c75242055338b59"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP CONSTRAINT "FK_5a4866f304edf4591ad785d34a4"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP CONSTRAINT "FK_1bb8b7dda222aa941c40bc681d2"`);
    }
}
