"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuestsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const guest_entity_1 = require("./guest.entity");
const uuid_1 = require("../utils/uuid");
let GuestsService = class GuestsService {
    constructor(guestsRepository) {
        this.guestsRepository = guestsRepository;
    }
    async findAll() {
        return this.guestsRepository.find({
            where: { isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(uuid) {
        const guest = await this.guestsRepository.findOne({
            where: { uuid, isDeleted: false },
        });
        if (!guest) {
            throw new common_1.NotFoundException(`Guest with UUID ${uuid} not found`);
        }
        return guest;
    }
    async findByStoreId(storeId) {
        return this.guestsRepository.find({
            where: { storeId, isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async create(createGuestDto) {
        const { userId, ...guestData } = createGuestDto;
        const guest = this.guestsRepository.create({
            ...guestData,
            uuid: (0, uuid_1.generateUUID7)(),
            createdBy: userId?.toString(),
        });
        return this.guestsRepository.save(guest);
    }
    async update(uuid, updateGuestDto) {
        const guest = await this.findOne(uuid);
        Object.assign(guest, updateGuestDto);
        return this.guestsRepository.save(guest);
    }
    async remove(uuid) {
        const guest = await this.findOne(uuid);
        guest.isDeleted = true;
        await this.guestsRepository.save(guest);
    }
    async findOrCreate(guestData) {
        let existingGuest = null;
        if (guestData.email) {
            existingGuest = await this.guestsRepository.findOne({
                where: {
                    email: guestData.email,
                    storeId: guestData.storeId,
                    isDeleted: false
                },
            });
        }
        if (!existingGuest && guestData.phone) {
            existingGuest = await this.guestsRepository.findOne({
                where: {
                    phone: guestData.phone,
                    storeId: guestData.storeId,
                    isDeleted: false
                },
            });
        }
        if (existingGuest) {
            Object.assign(existingGuest, guestData);
            return this.guestsRepository.save(existingGuest);
        }
        return this.create(guestData);
    }
};
exports.GuestsService = GuestsService;
exports.GuestsService = GuestsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(guest_entity_1.Guest)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], GuestsService);
//# sourceMappingURL=guests.service.js.map