"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuestsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guests_service_1 = require("./guests.service");
const guest_entity_1 = require("./guest.entity");
let GuestsController = class GuestsController {
    constructor(guestsService) {
        this.guestsService = guestsService;
    }
    create(createGuestDto) {
        return this.guestsService.create(createGuestDto);
    }
    findAll(storeId) {
        if (storeId) {
            return this.guestsService.findByStoreId(storeId);
        }
        return this.guestsService.findAll();
    }
    findOne(uuid) {
        return this.guestsService.findOne(uuid);
    }
    update(uuid, updateGuestDto) {
        return this.guestsService.update(uuid, updateGuestDto);
    }
    remove(uuid) {
        return this.guestsService.remove(uuid);
    }
    findOrCreate(guestData) {
        return this.guestsService.findOrCreate(guestData);
    }
};
exports.GuestsController = GuestsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new guest' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Guest created successfully', type: guest_entity_1.Guest }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GuestsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all guests' }),
    (0, swagger_1.ApiQuery)({ name: 'storeId', required: false, description: 'Filter by store ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of guests', type: [guest_entity_1.Guest] }),
    __param(0, (0, common_1.Query)('storeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], GuestsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':uuid'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a guest by UUID' }),
    (0, swagger_1.ApiParam)({ name: 'uuid', description: 'Guest UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Guest found', type: guest_entity_1.Guest }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Guest not found' }),
    __param(0, (0, common_1.Param)('uuid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], GuestsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':uuid'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a guest' }),
    (0, swagger_1.ApiParam)({ name: 'uuid', description: 'Guest UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Guest updated successfully', type: guest_entity_1.Guest }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Guest not found' }),
    __param(0, (0, common_1.Param)('uuid')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], GuestsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':uuid'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a guest (soft delete)' }),
    (0, swagger_1.ApiParam)({ name: 'uuid', description: 'Guest UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Guest deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Guest not found' }),
    __param(0, (0, common_1.Param)('uuid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], GuestsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('find-or-create'),
    (0, swagger_1.ApiOperation)({ summary: 'Find existing guest or create new one' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Guest found or created', type: guest_entity_1.Guest }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], GuestsController.prototype, "findOrCreate", null);
exports.GuestsController = GuestsController = __decorate([
    (0, swagger_1.ApiTags)('guests'),
    (0, common_1.Controller)('guests'),
    __metadata("design:paramtypes", [guests_service_1.GuestsService])
], GuestsController);
//# sourceMappingURL=guests.controller.js.map