import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne } from 'typeorm';
import { generateUUID7 } from '../utils/uuid';

@Entity('guests')
export class Guest {
  @PrimaryColumn({ type: 'varchar' })
  uuid: string;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  state: string;

  @Column({ type: 'varchar', nullable: true })
  zipCode: string;

  @Column({ type: 'varchar', nullable: true })
  country: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'varchar', nullable: true })
  sessionData: string; // For storing session-specific data

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint', nullable: true })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  storeId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.createdGuests)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedGuests)
  updatedByUser: any;

  @ManyToOne('Store', (store: any) => store.guests)
  store: any;

  @OneToMany('Order', (order: any) => order.guest)
  orders: any[];

  @OneToMany('Conversation', (conversation: any) => conversation.guest)
  conversations: any[];

  @OneToMany('Message', (message: any) => message.guest)
  messages: any[];

  @OneToMany('ToolCall', (toolCall: any) => toolCall.guest)
  toolCalls: any[];

  constructor() {
    if (!this.uuid) {
      this.uuid = generateUUID7();
    }
  }
}
