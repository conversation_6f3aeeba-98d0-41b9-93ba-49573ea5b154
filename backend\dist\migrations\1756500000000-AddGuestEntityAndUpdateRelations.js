"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGuestEntityAndUpdateRelations1756500000000 = void 0;
class AddGuestEntityAndUpdateRelations1756500000000 {
    constructor() {
        this.name = 'AddGuestEntityAndUpdateRelations1756500000000';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "guests" ("uuid" character varying NOT NULL, "name" character varying, "email" character varying, "phone" character varying, "address" text, "city" character varying, "state" character varying, "zipCode" character varying, "country" character varying, "notes" text, "sessionData" character varying, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" bigint, "updatedBy" bigint, "storeId" bigint NOT NULL, CONSTRAINT "PK_guests_uuid" PRIMARY KEY ("uuid"))`);
        await queryRunner.query(`ALTER TABLE "guests" ADD CONSTRAINT "FK_guests_createdBy" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "guests" ADD CONSTRAINT "FK_guests_updatedBy" FOREIGN KEY ("updatedBy") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "guests" ADD CONSTRAINT "FK_guests_storeId" FOREIGN KEY ("storeId") REFERENCES "stores"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations" ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD "customerId" bigint`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD CONSTRAINT "FK_conversations_customerId" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversations" ADD CONSTRAINT "FK_conversations_guestUuid" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "customerId" bigint`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_messages_customerId" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_messages_guestUuid" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ALTER COLUMN "userId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD "customerId" bigint`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD CONSTRAINT "FK_tool_calls_customerId" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ADD CONSTRAINT "FK_tool_calls_guestUuid" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "customerId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "guestUuid" character varying`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_guestUuid" FOREIGN KEY ("guestUuid") REFERENCES "guests"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_guestUuid"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "customerId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP CONSTRAINT "FK_tool_calls_guestUuid"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP CONSTRAINT "FK_tool_calls_customerId"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" DROP COLUMN "customerId"`);
        await queryRunner.query(`ALTER TABLE "tool_calls" ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_messages_guestUuid"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_messages_customerId"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "customerId"`);
        await queryRunner.query(`ALTER TABLE "messages" ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP CONSTRAINT "FK_conversations_guestUuid"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP CONSTRAINT "FK_conversations_customerId"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "guestUuid"`);
        await queryRunner.query(`ALTER TABLE "conversations" DROP COLUMN "customerId"`);
        await queryRunner.query(`ALTER TABLE "conversations" ALTER COLUMN "userId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "guests" DROP CONSTRAINT "FK_guests_storeId"`);
        await queryRunner.query(`ALTER TABLE "guests" DROP CONSTRAINT "FK_guests_updatedBy"`);
        await queryRunner.query(`ALTER TABLE "guests" DROP CONSTRAINT "FK_guests_createdBy"`);
        await queryRunner.query(`DROP TABLE "guests"`);
    }
}
exports.AddGuestEntityAndUpdateRelations1756500000000 = AddGuestEntityAndUpdateRelations1756500000000;
//# sourceMappingURL=1756500000000-AddGuestEntityAndUpdateRelations.js.map