import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { GuestsService } from './guests.service';
import { Guest } from './guest.entity';

@ApiTags('guests')
@Controller('guests')
export class GuestsController {
  constructor(private readonly guestsService: GuestsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new guest' })
  @ApiResponse({ status: 201, description: 'Guest created successfully', type: Guest })
  create(@Body() createGuestDto: Partial<Guest>) {
    return this.guestsService.create(createGuestDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all guests' })
  @ApiQuery({ name: 'storeId', required: false, description: 'Filter by store ID' })
  @ApiResponse({ status: 200, description: 'List of guests', type: [Guest] })
  findAll(@Query('storeId') storeId?: string) {
    if (storeId) {
      return this.guestsService.findByStoreId(storeId);
    }
    return this.guestsService.findAll();
  }

  @Get(':uuid')
  @ApiOperation({ summary: 'Get a guest by UUID' })
  @ApiParam({ name: 'uuid', description: 'Guest UUID' })
  @ApiResponse({ status: 200, description: 'Guest found', type: Guest })
  @ApiResponse({ status: 404, description: 'Guest not found' })
  findOne(@Param('uuid') uuid: string) {
    return this.guestsService.findOne(uuid);
  }

  @Patch(':uuid')
  @ApiOperation({ summary: 'Update a guest' })
  @ApiParam({ name: 'uuid', description: 'Guest UUID' })
  @ApiResponse({ status: 200, description: 'Guest updated successfully', type: Guest })
  @ApiResponse({ status: 404, description: 'Guest not found' })
  update(@Param('uuid') uuid: string, @Body() updateGuestDto: Partial<Guest>) {
    return this.guestsService.update(uuid, updateGuestDto);
  }

  @Delete(':uuid')
  @ApiOperation({ summary: 'Delete a guest (soft delete)' })
  @ApiParam({ name: 'uuid', description: 'Guest UUID' })
  @ApiResponse({ status: 200, description: 'Guest deleted successfully' })
  @ApiResponse({ status: 404, description: 'Guest not found' })
  remove(@Param('uuid') uuid: string) {
    return this.guestsService.remove(uuid);
  }

  @Post('find-or-create')
  @ApiOperation({ summary: 'Find existing guest or create new one' })
  @ApiResponse({ status: 200, description: 'Guest found or created', type: Guest })
  findOrCreate(@Body() guestData: Partial<Guest> & { storeId: string }) {
    return this.guestsService.findOrCreate(guestData);
  }
}
