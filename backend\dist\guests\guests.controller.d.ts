import { GuestsService } from './guests.service';
import { Guest } from './guest.entity';
export declare class GuestsController {
    private readonly guestsService;
    constructor(guestsService: GuestsService);
    create(createGuestDto: Partial<Guest>): Promise<Guest>;
    findAll(storeId?: string): Promise<Guest[]>;
    findOne(uuid: string): Promise<Guest>;
    update(uuid: string, updateGuestDto: Partial<Guest>): Promise<Guest>;
    remove(uuid: string): Promise<void>;
    findOrCreate(guestData: Partial<Guest> & {
        storeId: string;
    }): Promise<Guest>;
}
