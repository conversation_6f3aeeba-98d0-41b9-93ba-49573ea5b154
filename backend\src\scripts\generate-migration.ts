#!/usr/bin/env tsx

import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { join } from 'path';
import { writeFileSync, mkdirSync, existsSync } from 'fs';

// Load environment variables
config();

async function generateMigration() {
  const args = process.argv.slice(2);
  const migrationName = args.find(arg => arg.startsWith('--name='))?.split('=')[1] || 'Migration';
  
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'teno_store_db',
    synchronize: false,
    logging: false,
    entities: [join(__dirname, '../**/*.entity{.ts,.js}')],
    migrations: [join(__dirname, '../migrations/*{.ts,.js}')],
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    await dataSource.initialize();
    console.log('✅ Database connection established');

    // Generate migration
    const sqlInMemory = await dataSource.driver.createSchemaBuilder().log();
    
    if (sqlInMemory.upQueries.length === 0) {
      console.log('ℹ️  No schema changes detected');
      return;
    }

    // Create migrations directory if it doesn't exist
    const migrationsDir = join(__dirname, '../migrations');
    if (!existsSync(migrationsDir)) {
      mkdirSync(migrationsDir, { recursive: true });
    }

    // Generate migration filename with timestamp
    const timestamp = Date.now();
    const filename = `${timestamp}-${migrationName}.ts`;
    const filepath = join(migrationsDir, filename);

    // Generate migration content
    const migrationContent = `import { MigrationInterface, QueryRunner } from "typeorm";

export class ${migrationName}${timestamp} implements MigrationInterface {
    name = '${migrationName}${timestamp}'

    public async up(queryRunner: QueryRunner): Promise<void> {
${sqlInMemory.upQueries.map(query => `        await queryRunner.query(\`${query.query}\`);`).join('\n')}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
${sqlInMemory.downQueries.map(query => `        await queryRunner.query(\`${query.query}\`);`).join('\n')}
    }
}
`;

    // Write migration file
    writeFileSync(filepath, migrationContent);
    console.log(`✅ Migration generated: ${filename}`);
    console.log(`📁 Location: ${filepath}`);
    console.log(`🔄 Up queries: ${sqlInMemory.upQueries.length}`);
    console.log(`↩️  Down queries: ${sqlInMemory.downQueries.length}`);

    await dataSource.destroy();
    console.log('✅ Database connection closed');
  } catch (error) {
    console.error('❌ Migration generation failed:', error);
    process.exit(1);
  }
}

generateMigration();
