import { Repository } from 'typeorm';
import { Guest } from './guest.entity';
export declare class GuestsService {
    private guestsRepository;
    constructor(guestsRepository: Repository<Guest>);
    findAll(): Promise<Guest[]>;
    findOne(uuid: string): Promise<Guest>;
    findByStoreId(storeId: string): Promise<Guest[]>;
    create(createGuestDto: Partial<Guest> & {
        userId?: string | number;
    }): Promise<Guest>;
    update(uuid: string, updateGuestDto: Partial<Guest>): Promise<Guest>;
    remove(uuid: string): Promise<void>;
    findOrCreate(guestData: Partial<Guest> & {
        storeId: string;
    }): Promise<Guest>;
}
