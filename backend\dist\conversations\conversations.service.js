"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const conversation_entity_1 = require("./conversation.entity");
const message_entity_1 = require("./message.entity");
const tool_call_entity_1 = require("./tool-call.entity");
const uuid_1 = require("../utils/uuid");
const conversation_service_1 = require("../agents/core/conversation.service");
const database_service_1 = require("../shared/database.service");
let ConversationsService = class ConversationsService {
    constructor(conversationsRepository, messagesRepository, toolCallsRepository, databaseService) {
        this.conversationsRepository = conversationsRepository;
        this.messagesRepository = messagesRepository;
        this.toolCallsRepository = toolCallsRepository;
        this.databaseService = databaseService;
    }
    async findAll() {
        return this.conversationsRepository.find({
            where: { isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const conversation = await this.conversationsRepository.findOne({
            where: { id, isDeleted: false },
            relations: ['messages', 'toolCalls'],
        });
        if (!conversation) {
            throw new common_1.NotFoundException(`Conversation with ID ${id} not found`);
        }
        return conversation;
    }
    async findByStoreId(storeId) {
        return this.conversationsRepository.find({
            where: { storeId, isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findByUserId(userId) {
        return this.conversationsRepository.find({
            where: { userId, isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findByUuid(uuid) {
        const conversation = await this.conversationsRepository.findOne({
            where: { uuid, isDeleted: false },
            relations: ['messages', 'toolCalls'],
        });
        if (!conversation) {
            throw new common_1.NotFoundException(`Conversation with UUID ${uuid} not found`);
        }
        return conversation;
    }
    async create(createConversationDto) {
        const { userId, storeId, createdBy, ...conversationData } = createConversationDto;
        console.log('Creating conversation with data:', {
            userId,
            storeId,
            createdBy,
            conversationData,
            userIdString: userId?.toString(),
            storeIdString: storeId?.toString()
        });
        const conversation = this.conversationsRepository.create({
            ...conversationData,
            uuid: (0, uuid_1.generateUUID7)(),
            userId: userId?.toString(),
            storeId: storeId?.toString(),
            createdBy: (createdBy || userId)?.toString(),
        });
        console.log('Created conversation object:', conversation);
        return this.conversationsRepository.save(conversation);
    }
    async update(id, updateConversationDto) {
        const conversation = await this.findOne(id);
        Object.assign(conversation, updateConversationDto);
        return this.conversationsRepository.save(conversation);
    }
    async remove(id) {
        const conversation = await this.findOne(id);
        conversation.isDeleted = true;
        await this.conversationsRepository.save(conversation);
    }
    async getUnifiedTimeline(id, page = 1, limit = 50) {
        const conversation = await this.findOne(id);
        const messages = await this.messagesRepository.find({
            where: {
                conversationId: id,
                isDeleted: false
            },
            relations: ['user'],
            order: { createdAt: 'ASC' },
        });
        const toolCalls = await this.toolCallsRepository.find({
            where: {
                conversationId: id,
                isDeleted: false
            },
            relations: ['createdByUser'],
            order: { createdAt: 'ASC' },
        });
        const timelineItems = [
            ...messages.map(message => {
                const metadata = message.metadata || {};
                return {
                    id: message.id,
                    type: 'message',
                    content: message.content,
                    role: message.role,
                    metadata: message.metadata,
                    createdAt: message.createdAt,
                    updatedAt: message.updatedAt,
                    userId: message.userId,
                    conversationId: message.conversationId,
                    cost: message.cost || metadata.cost || null,
                    executionTime: message.executionTime || metadata.executionTime || null,
                    inputTokens: message.inputTokens || metadata.inputTokens || null,
                    outputTokens: message.outputTokens || metadata.outputTokens || null,
                    agentId: metadata.agentId || null,
                    customerId: metadata.customerId || null,
                    imageUrl: metadata.imageUrl || null,
                    videoUrl: metadata.videoUrl || null,
                    attachmentUrl: metadata.attachmentUrl || null,
                    attachmentType: metadata.attachmentType || null,
                    user: message.user || null,
                };
            }),
            ...toolCalls.map(toolCall => ({
                id: toolCall.id,
                type: 'tool_call',
                toolName: toolCall.toolName,
                toolInput: toolCall.parameters,
                toolOutput: toolCall.result,
                parameters: toolCall.parameters,
                result: toolCall.result,
                status: toolCall.status,
                success: toolCall.status === 'completed',
                error: toolCall.error,
                errorMessage: toolCall.error,
                duration: toolCall.duration,
                executionTime: toolCall.duration,
                createdAt: toolCall.createdAt,
                updatedAt: toolCall.updatedAt,
                userId: toolCall.userId,
                conversationId: toolCall.conversationId,
                cost: toolCall.cost || null,
                inputTokens: toolCall.inputTokens || null,
                outputTokens: toolCall.outputTokens || null,
                createdByUser: toolCall.createdByUser || null,
            }))
        ];
        timelineItems.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        const total = timelineItems.length;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const timeline = timelineItems.slice(startIndex, endIndex);
        return {
            timeline,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async getUnifiedTimelineByUuid(uuid, page = 1, limit = 50) {
        const conversation = await this.findByUuid(uuid);
        return this.getUnifiedTimeline(conversation.id, page, limit);
    }
    async getTimeline(id, page = 1, limit = 50) {
        return this.getUnifiedTimeline(id, page, limit);
    }
    async addMessage(conversationId, messageData) {
        const conversation = await this.findOne(conversationId);
        let role = 'user';
        if (messageData.agentId) {
            role = 'assistant';
        }
        else if (messageData.customerId) {
            role = 'user';
        }
        const metadata = {};
        if (messageData.agentId)
            metadata.agentId = messageData.agentId;
        if (messageData.customerId)
            metadata.customerId = messageData.customerId;
        if (messageData.imageUrl)
            metadata.imageUrl = messageData.imageUrl;
        if (messageData.videoUrl)
            metadata.videoUrl = messageData.videoUrl;
        if (messageData.attachmentUrl)
            metadata.attachmentUrl = messageData.attachmentUrl;
        if (messageData.attachmentType)
            metadata.attachmentType = messageData.attachmentType;
        if (messageData.cost)
            metadata.cost = messageData.cost;
        if (messageData.executionTime)
            metadata.executionTime = messageData.executionTime;
        if (messageData.inputTokens)
            metadata.inputTokens = messageData.inputTokens;
        if (messageData.outputTokens)
            metadata.outputTokens = messageData.outputTokens;
        const message = this.messagesRepository.create({
            content: messageData.content,
            role,
            metadata: Object.keys(metadata).length > 0 ? metadata : null,
            conversationId,
            userId: messageData.userId || messageData.createdBy,
            createdBy: messageData.createdBy,
            cost: messageData.cost || null,
            executionTime: messageData.executionTime || null,
            inputTokens: messageData.inputTokens || null,
            outputTokens: messageData.outputTokens || null,
        });
        return this.messagesRepository.save(message);
    }
    async addMessageByUuid(uuid, messageData) {
        const conversation = await this.findByUuid(uuid);
        const savedMessage = await this.addMessage(conversation.id, messageData);
        const isUserMessage = !messageData.agentId || messageData.agentId === 'customer-message';
        if (isUserMessage && messageData.content) {
            try {
                let userMessageContent = messageData.content;
                const customerNameMatch = messageData.content.match(/^\[([^\]]+)\]:\s*(.*)$/);
                if (customerNameMatch) {
                    userMessageContent = customerNameMatch[2];
                }
                setImmediate(async () => {
                    try {
                        await (0, conversation_service_1.processUserMessage)(this.databaseService.getDataSource(), {
                            conversationUuid: uuid,
                            userMessage: userMessageContent,
                            userId: messageData.createdBy,
                            agentId: 'conversation-agent',
                        });
                    }
                    catch (error) {
                        console.error('Failed to generate agent response:', error);
                    }
                });
            }
            catch (error) {
                console.error('Error triggering agent response:', error);
            }
        }
        return savedMessage;
    }
    async findAllToolCalls() {
        return this.toolCallsRepository.find({
            where: { isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findToolCallById(id) {
        const toolCall = await this.toolCallsRepository.findOne({
            where: { id, isDeleted: false },
        });
        if (!toolCall) {
            throw new common_1.NotFoundException(`ToolCall with ID ${id} not found`);
        }
        return toolCall;
    }
    async findToolCallsByConversationId(conversationId) {
        return this.toolCallsRepository.find({
            where: { conversationId, isDeleted: false },
            order: { createdAt: 'ASC' },
        });
    }
    async createToolCall(createToolCallDto) {
        const { userId, ...toolCallData } = createToolCallDto;
        const toolCall = this.toolCallsRepository.create({
            ...toolCallData,
            createdBy: userId?.toString(),
        });
        return this.toolCallsRepository.save(toolCall);
    }
    async addToolCallByUuid(uuid, toolCallData) {
        const conversation = await this.findByUuid(uuid);
        const toolCall = this.toolCallsRepository.create({
            toolName: toolCallData.toolName,
            parameters: toolCallData.parameters,
            result: toolCallData.result,
            status: toolCallData.status || 'completed',
            error: toolCallData.error,
            duration: toolCallData.duration || 0,
            cost: toolCallData.cost,
            inputTokens: toolCallData.inputTokens,
            outputTokens: toolCallData.outputTokens,
            conversationId: conversation.id,
            userId: toolCallData.userId || toolCallData.createdBy,
            createdBy: toolCallData.createdBy,
        });
        return this.toolCallsRepository.save(toolCall);
    }
    async updateToolCall(id, updateToolCallDto) {
        const toolCall = await this.findToolCallById(id);
        Object.assign(toolCall, updateToolCallDto);
        return this.toolCallsRepository.save(toolCall);
    }
    async removeToolCall(id) {
        const toolCall = await this.findToolCallById(id);
        toolCall.isDeleted = true;
        await this.toolCallsRepository.save(toolCall);
    }
};
exports.ConversationsService = ConversationsService;
exports.ConversationsService = ConversationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(conversation_entity_1.Conversation)),
    __param(1, (0, typeorm_1.InjectRepository)(message_entity_1.Message)),
    __param(2, (0, typeorm_1.InjectRepository)(tool_call_entity_1.ToolCall)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        database_service_1.DatabaseService])
], ConversationsService);
//# sourceMappingURL=conversations.service.js.map